#include "../../include/authentication/_AllocateU_Node_List_nodUpairCBHPEAVCAsyncLogInfos_1403C7E90.h"
#include "../../include/common/RFProtocol.h"

// Original address: 0x1403C7E90
// Function: ??$_Allocate@U_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@YAPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@0@_KPEAU120@@Z

/*
 * Function: ??$_Allocate@U_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@YAPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@0@_KPEAU120@@Z
 * Address: 0x1403C7E90
 */

std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *__fastcall std::_Allocate<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>(unsigned __int64 _Count, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *__formal)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  std::bad_alloc v6; // [sp+20h] [bp-28h]@7
  unsigned __int64 v7; // [sp+50h] [bp+8h]@1

  v7 = _Count;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7 )
  {
    if ( 0xFFFFFFFFFFFFFFFFui64 / v7 < 0x20 )
    {
      std::bad_alloc::bad_alloc(&v6, 0i64);
      CxxThrowException_0(&v6, &TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v7 = 0i64;
  }
  return (std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *)operator new(32 * v7);
}

